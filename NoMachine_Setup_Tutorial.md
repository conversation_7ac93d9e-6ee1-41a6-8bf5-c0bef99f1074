# NoMachine Remote Desktop Setup Tutorial for Debian

## Overview
This tutorial provides step-by-step instructions for setting up NoMachine remote desktop server on Debian with a clean, optimized GNOME desktop environment at 2K resolution.

## Prerequisites
- Debian 12 (Bookworm) server
- Root or sudo access
- Internet connection
- Basic command line knowledge

## Table of Contents
1. [System Preparation](#system-preparation)
2. [NoMachine Installation](#nomachine-installation)
3. [Desktop Environment Setup](#desktop-environment-setup)
4. [NoMachine Configuration](#nomachine-configuration)
5. [User Management](#user-management)
6. [Display Configuration](#display-configuration)
7. [System Optimization](#system-optimization)
8. [Troubleshooting](#troubleshooting)
9. [Verification](#verification)

## System Preparation

### Update System
```bash
sudo apt update && sudo apt upgrade -y
```

### Install Essential Packages
```bash
sudo apt install -y wget curl gnupg2 software-properties-common
```

## NoMachine Installation

### Download and Install NoMachine
```bash
# Download NoMachine for Debian
wget https://download.nomachine.com/download/8.13/Linux/nomachine_8.13.1_1_amd64.deb

# Install NoMachine
sudo dpkg -i nomachine_8.13.1_1_amd64.deb

# Fix any dependency issues
sudo apt install -f -y

# Enable and start NoMachine service
sudo systemctl enable nxserver
sudo systemctl start nxserver
```

### Verify Installation
```bash
sudo systemctl status nxserver
sudo /usr/NX/bin/nxserver --status
```

## Desktop Environment Setup

### Install GNOME Desktop Environment
```bash
# Install GNOME desktop (minimal installation)
sudo apt install -y gnome-core gdm3

# Install essential GNOME applications
sudo apt install -y gnome-terminal gnome-control-center gnome-system-monitor
sudo apt install -y gnome-text-editor gnome-tweaks nautilus gnome-software
sudo apt install -y gnome-disk-utility
```

### Enable and Start GDM
```bash
# Enable GDM (critical for NoMachine framebuffer)
sudo systemctl enable gdm
sudo systemctl start gdm

# Verify GDM is running
sudo systemctl status gdm
```

## NoMachine Configuration

### Configure Server Settings
```bash
# Edit server configuration
sudo nano /usr/NX/etc/server.cfg
```

**Key Configuration Changes:**
```ini
# Enable automatic display creation
CreateDisplay 1

# Set virtual desktop mode
VirtualDesktopMode 2

# Set display resolution to 2K
DisplayGeometry 2560x1440

# Authentication methods
AcceptedAuthenticationMethods NX-password,SSH-system

# Enable password database
EnablePasswordDB 1
```

### Configure Node Settings
```bash
# Edit node configuration
sudo nano /usr/NX/etc/node.cfg
```

**Key Configuration Changes:**
```ini
# Set users directory path (avoid permission issues)
UsersDirectoryPath "/tmp/nxusers"
```

### Create Users Directory
```bash
sudo mkdir -p /tmp/nxusers
sudo chmod 777 /tmp/nxusers
```

## User Management

### Add Users to NoMachine
```bash
# Add existing system user to NoMachine
sudo /usr/NX/bin/nxserver --useradd [username]

# Set NoMachine password for user
sudo /usr/NX/bin/nxserver --passwd [username]

# List NoMachine users
sudo /usr/NX/bin/nxserver --userlist
```

### Fix User Permissions
```bash
# Ensure user home directory permissions
sudo chown -R [username]:[username] /home/<USER>

# Add user to video group (important for display access)
sudo usermod -a -G video [username]
```

## Display Configuration

### Install Video Drivers for Virtual Displays
```bash
# Install dummy video driver for headless systems
sudo apt install -y xserver-xorg-video-dummy xserver-xorg-video-fbdev xserver-xorg-video-vesa
```

### Configure X11 Dummy Driver
```bash
# Create X11 configuration directory
sudo mkdir -p /etc/X11/xorg.conf.d

# Create dummy driver configuration
sudo tee /etc/X11/xorg.conf.d/20-dummy.conf > /dev/null << 'EOF'
Section "Monitor"
    Identifier "Monitor0"
    HorizSync 28.0-80.0
    VertRefresh 48.0-75.0
    Modeline "2560x1440_60.00" 312.25 2560 2752 3024 3488 1440 1443 1448 1493 -HSync +Vsync
EndSection

Section "Device"
    Identifier "Card0"
    Driver "dummy"
    VideoRam 512000
EndSection

Section "Screen"
    Identifier "Screen0"
    Device "Card0"
    Monitor "Monitor0"
    DefaultDepth 24
    SubSection "Display"
        Depth 24
        Modes "2560x1440"
    EndSubSection
EndSection
EOF
```

### Restart Services
```bash
# Restart NoMachine service
sudo systemctl restart nxserver

# Verify service status
sudo systemctl status nxserver
```

## System Optimization

### Remove Unnecessary Desktop Components
```bash
# Remove media applications
sudo apt remove --purge -y totem totem-common totem-plugins

# Remove unnecessary GNOME applications
sudo apt remove --purge -y gnome-contacts gnome-characters gnome-calculator
sudo apt remove --purge -y gnome-font-viewer gnome-sushi gnome-user-share gnome-user-docs

# Remove browser integration components
sudo apt remove --purge -y chrome-gnome-shell gnome-browser-connector
sudo apt remove --purge -y gnome-applets gnome-applets-data gnome-bluetooth-sendto

# Clean up automatically installed packages
sudo apt autoremove --purge -y

# Clean package cache
sudo apt autoclean
```

### Verify Clean Installation
```bash
# Check available desktop sessions
ls /usr/share/xsessions/

# Check disk usage
df -h /

# Verify no XFCE or other DE components remain
dpkg -l | grep -i xfce
```

## Troubleshooting

### Common Issues and Solutions

#### "Cannot create a new display" Error
**Cause:** NoMachine framebuffer cannot start without X server infrastructure.

**Solution:**
```bash
# Ensure GDM is running
sudo systemctl status gdm
sudo systemctl start gdm

# Check X displays are available
ls -la /tmp/.X11-unix/

# Restart NoMachine
sudo systemctl restart nxserver
```

#### "NXFrameBuffer failed to start" Error
**Cause:** Missing video drivers or X11 configuration.

**Solution:**
```bash
# Install video drivers
sudo apt install -y xserver-xorg-video-dummy

# Verify X11 configuration exists
ls -la /etc/X11/xorg.conf.d/20-dummy.conf

# Test manual X server start
sudo Xvfb :99 -screen 0 2560x1440x24 -ac &
ps aux | grep Xvfb
sudo kill [Xvfb_PID]
```

#### Permission Issues
**Solution:**
```bash
# Fix user directory permissions
sudo chown -R [username]:[username] /home/<USER>

# Ensure users directory exists
sudo mkdir -p /tmp/nxusers
sudo chmod 777 /tmp/nxusers

# Add user to required groups
sudo usermod -a -G video,users [username]
```

## Verification

### Test NoMachine Connection
1. **Install NoMachine Client** on your local machine
2. **Connect to server** using server IP address
3. **Login credentials:**
   - Username: [your_username]
   - Password: [nomachine_password]
4. **Select desktop environment:** GNOME
5. **Choose:** "Create a new virtual desktop"

### Expected Results
- ✅ 2K resolution (2560x1440) desktop
- ✅ Clean GNOME interface
- ✅ Essential applications available
- ✅ Smooth remote desktop experience

### Performance Verification
```bash
# Check NoMachine service status
sudo systemctl status nxserver

# Monitor system resources
htop

# Check display information
echo $DISPLAY
xrandr (if available in session)
```

## Final Configuration Summary

### NoMachine Server Settings
- **Resolution:** 2560x1440 (2K)
- **Desktop Environment:** GNOME
- **Virtual Desktop Mode:** Interactive (Mode 2)
- **Authentication:** NX-password + SSH-system
- **Display Creation:** Enabled

### System Components
- **Display Manager:** GDM
- **Desktop Sessions:** GNOME variants only
- **Video Driver:** Dummy driver for headless operation
- **User Directory:** /tmp/nxusers (avoids permission issues)

### Security Considerations
- NoMachine runs on port 4000 (default)
- Consider firewall configuration for remote access
- Use strong passwords for NoMachine users
- Regular system updates recommended

## Maintenance

### Regular Tasks
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Check NoMachine service health
sudo systemctl status nxserver

# Monitor disk usage
df -h /

# Clean package cache periodically
sudo apt autoclean
```

### Backup Important Configurations
```bash
# Backup NoMachine configuration
sudo cp /usr/NX/etc/server.cfg /usr/NX/etc/server.cfg.backup
sudo cp /usr/NX/etc/node.cfg /usr/NX/etc/node.cfg.backup

# Backup X11 configuration
sudo cp /etc/X11/xorg.conf.d/20-dummy.conf /etc/X11/xorg.conf.d/20-dummy.conf.backup
```

## Advanced Configuration

### VS Code Setup for Users
```bash
# Create desktop shortcut for VS Code
sudo -u [username] mkdir -p /home/<USER>/Desktop

# Create VS Code desktop entry
sudo tee /home/<USER>/Desktop/VSCode.desktop > /dev/null << 'EOF'
[Desktop Entry]
Name=Visual Studio Code
Comment=Code Editing. Redefined.
GenericName=Text Editor
Exec=/usr/share/code/code --user-data-dir=/home/<USER>/.vscode %F
Icon=/usr/share/code/resources/app/resources/linux/code.png
Type=Application
StartupNotify=true
StartupWMClass=Code
Categories=Development;IDE;
MimeType=text/plain;inode/directory;
Actions=new-empty-window;
Keywords=vscode;

[Desktop Action new-empty-window]
Name=New Empty Window
Exec=/usr/share/code/code --user-data-dir=/home/<USER>/.vscode --new-window %F
Icon=/usr/share/code/resources/app/resources/linux/code.png
EOF

# Set proper permissions
sudo chown [username]:[username] /home/<USER>/Desktop/VSCode.desktop
sudo chmod +x /home/<USER>/Desktop/VSCode.desktop

# Add command alias
sudo -u [username] bash -c 'echo "alias code=\"/usr/share/code/code --user-data-dir=/home/<USER>/.vscode\"" >> /home/<USER>/.bashrc'
```

### Resolution Customization
To change the resolution from 2K to other values:

```bash
# Edit NoMachine server configuration
sudo nano /usr/NX/etc/server.cfg

# Change DisplayGeometry line to desired resolution:
# For 1080p: DisplayGeometry 1920x1080
# For 1440p: DisplayGeometry 2560x1440
# For 4K: DisplayGeometry 3840x2160

# Update X11 dummy driver configuration accordingly
sudo nano /etc/X11/xorg.conf.d/20-dummy.conf

# Update the Modeline and Modes sections to match your resolution
# Use online modeline calculators for custom resolutions

# Restart NoMachine
sudo systemctl restart nxserver
```

### Multiple User Setup
```bash
# Create additional system users
sudo useradd -m -s /bin/bash [username2]
sudo passwd [username2]

# Add to required groups
sudo usermod -a -G video,users [username2]

# Add to NoMachine
sudo /usr/NX/bin/nxserver --useradd [username2]
sudo /usr/NX/bin/nxserver --passwd [username2]

# Fix permissions
sudo chown -R [username2]:[username2] /home/<USER>
```

## Network Configuration

### Firewall Setup (if using UFW)
```bash
# Install UFW if not present
sudo apt install -y ufw

# Allow NoMachine port
sudo ufw allow 4000/tcp

# Allow SSH (important!)
sudo ufw allow ssh

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status
```

### Custom Port Configuration
```bash
# Edit NoMachine server configuration
sudo nano /usr/NX/etc/server.cfg

# Find and modify:
# ServerPort 4000
# Change to your desired port

# Restart service
sudo systemctl restart nxserver

# Update firewall rules accordingly
sudo ufw allow [new_port]/tcp
sudo ufw delete allow 4000/tcp
```

## Performance Optimization

### System Resource Optimization
```bash
# Disable unnecessary services
sudo systemctl disable bluetooth
sudo systemctl stop bluetooth

# Optimize GNOME for performance
gsettings set org.gnome.desktop.interface enable-animations false
gsettings set org.gnome.desktop.interface gtk-theme 'Adwaita'
gsettings set org.gnome.shell.extensions.desktop-icons show-home false
gsettings set org.gnome.shell.extensions.desktop-icons show-trash false
```

### NoMachine Performance Tuning
```bash
# Edit server configuration for better performance
sudo nano /usr/NX/etc/server.cfg

# Add/modify these settings:
# EnableClipboard both
# EnableFileSharing 1
# EnablePrinting 0
# EnableAudio 0  # Disable if not needed
# EnableUSBDeviceSharing 0  # Disable if not needed
```

## Monitoring and Logging

### Log Locations
```bash
# NoMachine server logs
sudo tail -f /usr/NX/var/log/server.log

# NoMachine error logs
sudo tail -f /usr/NX/var/log/nxerror.log

# System logs for NoMachine service
sudo journalctl -u nxserver -f

# GDM logs
sudo journalctl -u gdm -f
```

### Health Check Script
```bash
# Create monitoring script
sudo tee /usr/local/bin/nomachine-health.sh > /dev/null << 'EOF'
#!/bin/bash
echo "=== NoMachine Health Check ==="
echo "Date: $(date)"
echo ""

echo "NoMachine Service Status:"
systemctl is-active nxserver
echo ""

echo "GDM Service Status:"
systemctl is-active gdm
echo ""

echo "Available X Displays:"
ls -la /tmp/.X11-unix/ 2>/dev/null || echo "No X displays found"
echo ""

echo "NoMachine Users:"
/usr/NX/bin/nxserver --userlist
echo ""

echo "Disk Usage:"
df -h / | tail -1
echo ""

echo "Memory Usage:"
free -h
echo ""

echo "Recent NoMachine Errors:"
tail -5 /usr/NX/var/log/nxerror.log 2>/dev/null || echo "No recent errors"
EOF

# Make executable
sudo chmod +x /usr/local/bin/nomachine-health.sh

# Run health check
sudo /usr/local/bin/nomachine-health.sh
```

## Backup and Recovery

### Configuration Backup
```bash
# Create backup directory
sudo mkdir -p /backup/nomachine

# Backup NoMachine configurations
sudo cp -r /usr/NX/etc/ /backup/nomachine/
sudo cp -r /etc/X11/xorg.conf.d/ /backup/nomachine/

# Backup user data
sudo tar -czf /backup/nomachine/user-data-$(date +%Y%m%d).tar.gz /home/<USER>/Desktop /home/<USER>/.bashrc

# Create backup script
sudo tee /usr/local/bin/backup-nomachine.sh > /dev/null << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/nomachine"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup configurations
cp -r /usr/NX/etc/ $BACKUP_DIR/nx-config-$DATE/
cp -r /etc/X11/xorg.conf.d/ $BACKUP_DIR/x11-config-$DATE/

# Backup user list
/usr/NX/bin/nxserver --userlist > $BACKUP_DIR/users-$DATE.txt

echo "Backup completed: $BACKUP_DIR"
EOF

sudo chmod +x /usr/local/bin/backup-nomachine.sh
```

### Recovery Procedures
```bash
# Restore NoMachine configuration
sudo systemctl stop nxserver
sudo cp -r /backup/nomachine/nx-config-[DATE]/* /usr/NX/etc/
sudo systemctl start nxserver

# Restore X11 configuration
sudo cp -r /backup/nomachine/x11-config-[DATE]/* /etc/X11/xorg.conf.d/

# Recreate users from backup
# (Manual process - refer to users-[DATE].txt file)
```

## Security Hardening

### Enhanced Security Settings
```bash
# Edit NoMachine server configuration
sudo nano /usr/NX/etc/server.cfg

# Add security enhancements:
# AcceptedAuthenticationMethods NX-password
# EnablePasswordDB 1
# PasswordAuthentication 1
# RSAAuthentication 0
# EnableTcpNoDelay 1
# EnableSSHAuthentication 0  # If not using SSH auth
```

### User Access Control
```bash
# Limit NoMachine access to specific users
sudo nano /usr/NX/etc/server.cfg

# Add user restrictions:
# AuthorizedUserDB /usr/NX/etc/users.db

# Create authorized users file
sudo touch /usr/NX/etc/users.db
echo "[username1]" | sudo tee -a /usr/NX/etc/users.db
echo "[username2]" | sudo tee -a /usr/NX/etc/users.db
```

---

## Conclusion

This tutorial provides a complete setup for NoMachine remote desktop server on Debian with:

- ✅ **Optimized GNOME Desktop** at 2K resolution
- ✅ **Clean, minimal installation** without unnecessary components
- ✅ **Proper video driver configuration** for headless operation
- ✅ **Comprehensive troubleshooting** guide
- ✅ **Performance optimization** settings
- ✅ **Security hardening** options
- ✅ **Monitoring and maintenance** procedures

### Key Success Factors
1. **GDM must be running** - Critical for framebuffer initialization
2. **Proper video drivers** - Dummy driver for headless systems
3. **Correct permissions** - User directories and group memberships
4. **Clean desktop environment** - Remove unnecessary components for better performance

### Support and Updates
- Keep system packages updated regularly
- Monitor NoMachine logs for issues
- Backup configurations before making changes
- Test connections after any system updates

**Your NoMachine server is now ready for production use!**
