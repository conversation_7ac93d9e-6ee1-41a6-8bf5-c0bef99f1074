# NoMachine Quick Reference Card

## Essential Commands

### Service Management
```bash
# Check NoMachine status
sudo systemctl status nxserver

# Start/Stop/Restart NoMachine
sudo systemctl start nxserver
sudo systemctl stop nxserver
sudo systemctl restart nxserver

# Check NoMachine server status
sudo /usr/NX/bin/nxserver --status
```

### User Management
```bash
# Add user to NoMachine
sudo /usr/NX/bin/nxserver --useradd [username]

# Set NoMachine password
sudo /usr/NX/bin/nxserver --passwd [username]

# List NoMachine users
sudo /usr/NX/bin/nxserver --userlist

# Remove user from NoMachine
sudo /usr/NX/bin/nxserver --userdel [username]
```

### Configuration Files
```bash
# Main server configuration
/usr/NX/etc/server.cfg

# Node configuration
/usr/NX/etc/node.cfg

# X11 dummy driver configuration
/etc/X11/xorg.conf.d/20-dummy.conf
```

### Log Files
```bash
# Main server log
sudo tail -f /usr/NX/var/log/server.log

# Error log
sudo tail -f /usr/NX/var/log/nxerror.log

# System service logs
sudo journalctl -u nxserver -f
```

### Troubleshooting Commands
```bash
# Check GDM status (critical!)
sudo systemctl status gdm

# Check X displays
ls -la /tmp/.X11-unix/

# Check user permissions
groups [username]
ls -la /home/<USER>/

# Test X server manually
sudo Xvfb :99 -screen 0 2560x1440x24 -ac &
```

## Key Configuration Settings

### Server Configuration (/usr/NX/etc/server.cfg)
```ini
CreateDisplay 1
VirtualDesktopMode 2
DisplayGeometry 2560x1440
AcceptedAuthenticationMethods NX-password,SSH-system
EnablePasswordDB 1
```

### Node Configuration (/usr/NX/etc/node.cfg)
```ini
UsersDirectoryPath "/tmp/nxusers"
```

## Common Issues & Quick Fixes

### "Cannot create a new display"
```bash
sudo systemctl start gdm
sudo systemctl restart nxserver
```

### "NXFrameBuffer failed to start"
```bash
sudo apt install -y xserver-xorg-video-dummy
sudo systemctl restart nxserver
```

### Permission Issues
```bash
sudo chown -R [username]:[username] /home/<USER>
sudo usermod -a -G video [username]
sudo mkdir -p /tmp/nxusers && sudo chmod 777 /tmp/nxusers
```

## Installation Quick Steps

### 1. Install NoMachine
```bash
wget https://download.nomachine.com/download/8.13/Linux/nomachine_8.13.1_1_amd64.deb
sudo dpkg -i nomachine_8.13.1_1_amd64.deb
sudo apt install -f -y
```

### 2. Install GNOME & GDM
```bash
sudo apt install -y gnome-core gdm3
sudo systemctl enable gdm
sudo systemctl start gdm
```

### 3. Configure Video Driver
```bash
sudo apt install -y xserver-xorg-video-dummy
sudo mkdir -p /etc/X11/xorg.conf.d
# Create 20-dummy.conf (see full tutorial)
```

### 4. Configure NoMachine
```bash
# Edit /usr/NX/etc/server.cfg
# Edit /usr/NX/etc/node.cfg
sudo mkdir -p /tmp/nxusers && sudo chmod 777 /tmp/nxusers
sudo systemctl restart nxserver
```

### 5. Add Users
```bash
sudo /usr/NX/bin/nxserver --useradd [username]
sudo /usr/NX/bin/nxserver --passwd [username]
```

## Health Check Script
```bash
#!/bin/bash
echo "=== NoMachine Health Check ==="
echo "NoMachine: $(systemctl is-active nxserver)"
echo "GDM: $(systemctl is-active gdm)"
echo "X Displays: $(ls /tmp/.X11-unix/ 2>/dev/null | wc -l)"
echo "Users: $(/usr/NX/bin/nxserver --userlist | wc -l)"
echo "Disk: $(df -h / | tail -1 | awk '{print $5}')"
```

## Connection Details
- **Default Port:** 4000
- **Protocol:** NX
- **Resolution:** 2560x1440 (2K)
- **Desktop:** GNOME
- **Authentication:** NX-password

## Backup Commands
```bash
# Backup configurations
sudo cp -r /usr/NX/etc/ /backup/nomachine-$(date +%Y%m%d)/
sudo cp -r /etc/X11/xorg.conf.d/ /backup/x11-$(date +%Y%m%d)/

# Backup user list
sudo /usr/NX/bin/nxserver --userlist > /backup/users-$(date +%Y%m%d).txt
```

---
**For complete setup instructions, see: NoMachine_Setup_Tutorial.md**
