# NoMachine Remote Desktop with VS Code Desktop Tutorial

## Overview
This tutorial provides a complete guide to install and configure NoMachine remote desktop server on Debian Linux, enabling remote access with VS Code desktop application support.

## Prerequisites
- Debian Linux system with root access
- Internet connection for downloading packages
- Basic terminal knowledge

## Part 1: NoMachine Installation

### 1.1 Download and Install NoMachine
```bash
# Download NoMachine (check https://www.nomachine.com for latest version)
cd /tmp
wget https://download.nomachine.com/download/9.0/Linux/nomachine_9.0.188-11_amd64.deb

# Install NoMachine
sudo dpkg -i nomachine_9.0.188-11_amd64.deb

# Fix any dependency issues if they occur
sudo apt-get install -f
```

### 1.2 Start and Enable NoMachine Service
```bash
# Enable and start the service
sudo systemctl enable nxserver
sudo systemctl start nxserver

# Verify service is running
sudo systemctl status nxserver
```

## Part 2: Firewall Configuration

### 2.1 Configure UFW for NoMachine
```bash
# Allow NoMachine port (4000/tcp)
sudo ufw allow 4000/tcp comment "NoMachine"

# Check firewall status
sudo ufw status
```

## Part 3: NoMachine Server Configuration

### 3.1 Backup Original Configuration
```bash
sudo cp /usr/NX/etc/server.cfg /usr/NX/etc/server.cfg.backup
```

### 3.2 Configure Authentication and Virtual Desktop
```bash
# Edit the configuration file
sudo nano /usr/NX/etc/server.cfg

# Make these changes:
# 1. Enable password authentication:
EnablePasswordDB 1

# 2. Set authentication methods:
AcceptedAuthenticationMethods NX-password,SSH-system

# 3. Enable virtual desktop creation:
CreateDisplay 1

# 4. Allow any authenticated user to create displays (comment out DisplayOwner):
#DisplayOwner "root"

# 5. Set display geometry for better compatibility:
DisplayGeometry 1920x1080

# 6. Enable virtual desktop mode:
VirtualDesktopMode 2
```

### 3.3 Fix NoMachine User Permissions
```bash
# Add nx user to video group for framebuffer access
sudo usermod -a -G video nx

# Clean up any stale X server sockets
sudo rm -f /tmp/.X11-unix/X*

# Verify nx user permissions
groups nx
```

### 3.4 Restart NoMachine Service
```bash
sudo systemctl restart nxserver
```

## Part 4: User Management

### 4.1 Add Root User to NoMachine Database
```bash
# Add root user with password authentication
sudo /usr/NX/bin/nxserver --useradd root --passworddb

# Set password for root user
sudo /usr/NX/bin/nxserver --passwd root
```

### 4.2 Add sysy User to NoMachine Database
```bash
# First, ensure sysy user exists on the system
sudo useradd -m sysy

# Set password for sysy system user
sudo passwd sysy

# Add sysy user to NoMachine database
sudo /usr/NX/bin/nxserver --useradd sysy --passworddb

# Set password for sysy user in NoMachine
sudo /usr/NX/bin/nxserver --passwd sysy
```

### 4.3 Remove Other Users (Optional)
```bash
# List current users
sudo /usr/NX/bin/nxserver --userlist

# Remove specific user if needed (replace 'username' with actual username)
sudo /usr/NX/bin/nxserver --userdel username
```

## Part 5: Desktop Environment Setup

### 5.1 Disable GDM (GNOME Display Manager)
```bash
# Disable GDM service (not needed for NoMachine virtual desktops)
sudo systemctl disable gdm
sudo systemctl stop gdm
```

### 5.2 Set Environment Variables
```bash
# Add DISPLAY variable to bashrc
echo 'export DISPLAY=:1001' >> ~/.bashrc
source ~/.bashrc
```

## Part 6: VS Code Desktop Configuration

### 6.1 Identify Available Displays
NoMachine creates virtual displays. Check which displays are available:
```bash
# Check X11 authentication list
xauth list

# Test display functionality
DISPLAY=:1001 xdpyinfo | head -5
```

### 6.2 Create VS Code Desktop Launcher
```bash
# Create desktop file
cat > /root/Desktop/VSCode.desktop << 'EOF'
[Desktop Entry]
Name=Visual Studio Code
Comment=Code Editing. Redefined.
GenericName=Text Editor
Exec=env DISPLAY=:1001 /usr/share/code/code --no-sandbox --user-data-dir=/root/.vscode-root %F
Icon=/usr/share/code/resources/app/resources/linux/code.png
Type=Application
StartupNotify=true
StartupWMClass=Code
Categories=Development;IDE;
MimeType=text/plain;inode/directory;
Actions=new-empty-window;
Keywords=vscode;

[Desktop Action new-empty-window]
Name=New Empty Window
Exec=env DISPLAY=:1001 /usr/share/code/code --no-sandbox --user-data-dir=/root/.vscode-root --new-window %F
Icon=/usr/share/code/resources/app/resources/linux/code.png
EOF

# Make it executable and trusted
chmod +x /root/Desktop/VSCode.desktop
```

### 6.3 Create Command Line Alias
```bash
# Add VS Code desktop alias to bashrc
echo 'alias code-desktop="DISPLAY=:1001 /usr/share/code/code --no-sandbox --user-data-dir=/root/.vscode-root"' >> ~/.bashrc
source ~/.bashrc
```

### 6.4 Configure VS Code for sysy User
```bash
# Create VS Code desktop file for sysy user
sudo mkdir -p /home/<USER>/Desktop

cat > /home/<USER>/Desktop/VSCode.desktop << 'EOF'
[Desktop Entry]
Name=Visual Studio Code
Comment=Code Editing. Redefined.
GenericName=Text Editor
Exec=env DISPLAY=:1001 /usr/share/code/code --user-data-dir=/home/<USER>/.vscode %F
Icon=/usr/share/code/resources/app/resources/linux/code.png
Type=Application
StartupNotify=true
StartupWMClass=Code
Categories=Development;IDE;
MimeType=text/plain;inode/directory;
Actions=new-empty-window;
Keywords=vscode;

[Desktop Action new-empty-window]
Name=New Empty Window
Exec=env DISPLAY=:1001 /usr/share/code/code --user-data-dir=/home/<USER>/.vscode --new-window %F
Icon=/usr/share/code/resources/app/resources/linux/code.png
EOF

# Set ownership and permissions
sudo chown sysy:sysy /home/<USER>/Desktop/VSCode.desktop
sudo chmod +x /home/<USER>/Desktop/VSCode.desktop

# Add VS Code alias for sysy user
sudo -u sysy bash -c 'echo "alias code-desktop=\"DISPLAY=:1001 /usr/share/code/code --user-data-dir=/home/<USER>/.vscode\"" >> /home/<USER>/.bashrc'
```

## Part 7: Connection and Usage

### 7.1 Connect to NoMachine
1. **Download NoMachine Client** on your local machine from https://www.nomachine.com
2. **Install the client** on your local computer
3. **Create new connection:**
   - Protocol: NX
   - Host: [Your server IP address]
   - Port: 4000
4. **Authentication:**
   - Username: root or sysy
   - Password: [Your root password or sysy password]
5. **Desktop Settings:**
   - Choose "Create a new virtual desktop"
   - Select GNOME as desktop environment

### 7.2 Launch VS Code Desktop
Once connected to NoMachine:

**Method 1: From GNOME Activities**
- Press Super key or click Activities
- Type "Visual Studio Code"
- Click the VS Code icon

**Method 2: From Terminal**
```bash
code-desktop
```

**Method 3: Direct Command**
```bash
DISPLAY=:1001 /usr/share/code/code --no-sandbox --user-data-dir=/root/.vscode-root
```

## Important Notes

### Display Management
- **NoMachine uses virtual displays** (e.g., :1001, :1002) - no traditional X11 server needed
- **Always use DISPLAY=:1001** for VS Code desktop (this is the working virtual display)
- **NoMachine creates its own X11 environment** - no Xorg installation required

### VS Code Flags Explained
- `--no-sandbox`: Required for running as root user
- `--user-data-dir=/root/.vscode-root`: Separate data directory for root user
- These flags prevent the "super user" warning and allow proper operation

### Troubleshooting

**VS Code won't start:**
```bash
# Check if correct display is being used
echo $DISPLAY

# Test display connectivity
DISPLAY=:1001 xdpyinfo | head -5

# Launch with verbose output
DISPLAY=:1001 /usr/share/code/code --no-sandbox --user-data-dir=/root/.vscode-root --verbose
```

**NoMachine connection issues:**
```bash
# Check service status
sudo systemctl status nxserver

# Check firewall
sudo ufw status

# Check port availability
sudo netstat -tlnp | grep :4000
```

**Authentication problems:**
```bash
# Verify user in database
sudo /usr/NX/bin/nxserver --userlist

# Reset user password
sudo /usr/NX/bin/nxserver --passwd root
```

## Security Considerations

1. **Change default SSH port** if SSH is enabled
2. **Use strong passwords** for NoMachine users
3. **Configure firewall rules** to restrict access to trusted IPs only
4. **Regular system updates** to maintain security
5. **Monitor NoMachine logs** for suspicious activity: `/usr/NX/var/log/`

## File Locations Reference

- **NoMachine Config**: `/usr/NX/etc/server.cfg`
- **NoMachine Logs**: `/usr/NX/var/log/`
- **Desktop File (root)**: `/root/Desktop/VSCode.desktop`
- **Desktop File (sysy)**: `/home/<USER>/Desktop/VSCode.desktop`
- **System Desktop Files**: `/usr/share/applications/`
- **VS Code Binary**: `/usr/share/code/code`
- **VS Code User Data (root)**: `/root/.vscode-root/`
- **VS Code User Data (sysy)**: `/home/<USER>/.vscode/`

## Conclusion

This setup provides a complete remote desktop solution with VS Code desktop application support. The configuration ensures:
- Secure remote access via NoMachine
- Proper virtual desktop environment
- VS Code desktop application functionality
- Root user authentication and safety

The setup is optimized for development work with full GUI access to VS Code and other desktop applications through NoMachine's virtual desktop technology.
