# NoMachine Remote Desktop Setup Documentation

This repository contains comprehensive documentation for setting up NoMachine remote desktop server on Debian with an optimized GNOME desktop environment.

## 📚 Documentation Files

### 🎯 [NoMachine_Setup_Tutorial.md](NoMachine_Setup_Tutorial.md)
**Complete step-by-step tutorial** for setting up NoMachine from scratch.

**Includes:**
- System preparation and package installation
- NoMachine server installation and configuration
- GNOME desktop environment setup
- 2K resolution configuration
- User management and permissions
- Troubleshooting guide
- Performance optimization
- Security hardening
- Backup and recovery procedures

### ⚡ [NoMachine_Quick_Reference.md](NoMachine_Quick_Reference.md)
**Quick reference card** with essential commands and configurations.

**Includes:**
- Service management commands
- User management commands
- Configuration file locations
- Common troubleshooting steps
- Health check script
- Backup commands

### 💻 [NoMachine_VSCode_Setup_Tutorial.md](NoMachine_VSCode_Setup_Tutorial.md)
**VS Code integration tutorial** for development environments.

**Includes:**
- VS Code installation and configuration
- Desktop shortcut creation
- User-specific settings
- Development environment optimization

## 🚀 Quick Start

1. **Follow the main tutorial**: [NoMachine_Setup_Tutorial.md](NoMachine_Setup_Tutorial.md)
2. **Use quick reference for daily operations**: [NoMachine_Quick_Reference.md](NoMachine_Quick_Reference.md)
3. **Set up VS Code if needed**: [NoMachine_VSCode_Setup_Tutorial.md](NoMachine_VSCode_Setup_Tutorial.md)

## ✨ What You'll Get

- **2K Resolution (2560x1440)** remote desktop
- **Clean GNOME desktop environment** optimized for server use
- **Reliable connection** with proper framebuffer support
- **Multi-user support** with proper permissions
- **Performance optimized** system with unnecessary components removed
- **Comprehensive troubleshooting** knowledge

## 🔧 System Requirements

- **OS**: Debian 12 (Bookworm)
- **RAM**: 2GB minimum, 4GB recommended
- **Disk**: 20GB minimum free space
- **Network**: Stable internet connection
- **Access**: Root or sudo privileges

## 🎯 Key Features

- ✅ **Headless server support** with dummy video driver
- ✅ **GDM integration** for proper X server infrastructure
- ✅ **Clean desktop environment** without unnecessary applications
- ✅ **2K resolution support** for high-DPI displays
- ✅ **Multi-user configuration** with proper isolation
- ✅ **Security hardening** options
- ✅ **Monitoring and logging** setup
- ✅ **Backup and recovery** procedures

## 🛠️ Troubleshooting

If you encounter issues:

1. **Check the troubleshooting section** in the main tutorial
2. **Use the quick reference** for common commands
3. **Run the health check script** to diagnose problems
4. **Check log files** for detailed error information

## 📋 Common Issues

- **"Cannot create a new display"** → Ensure GDM is running
- **"NXFrameBuffer failed to start"** → Install dummy video driver
- **Permission errors** → Fix user directory ownership and groups
- **Connection timeouts** → Check firewall and network settings

## 🔄 Maintenance

- **Regular updates**: Keep system packages updated
- **Monitor logs**: Check NoMachine logs for issues
- **Backup configurations**: Use provided backup scripts
- **Health checks**: Run monitoring scripts regularly

## 📞 Support

For issues not covered in the documentation:

1. Check NoMachine official documentation
2. Review system logs for detailed error messages
3. Verify all configuration steps were completed
4. Test with minimal configuration first

## 📝 Version History

- **v1.0**: Initial comprehensive setup tutorial
- **v1.1**: Added VS Code integration
- **v1.2**: Added quick reference and advanced configurations

---

**Happy remote desktop computing! 🖥️✨**
